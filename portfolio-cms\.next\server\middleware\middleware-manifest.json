{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "db2f537ae137962aa04a7614142b8f72", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b9af0eb3520bca4d74a33f00ff4d6f230378025d00ab7c08403cfc3f595200ea", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "71686de1746778dde99d4f68bf8339140aa594724861cae47629813da9549e8b"}}}, "instrumentation": null, "functions": {}}