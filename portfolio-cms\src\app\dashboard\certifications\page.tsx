'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, Award, Calendar, ExternalLink } from 'lucide-react'
import { toast } from 'sonner'

interface Certification {
  id: string
  title: string
  issuer: string
  date: string
  credentialId?: string
  emoji?: string
  description: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export default function CertificationsPage() {
  const [certifications, setCertifications] = useState<Certification[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCertifications()
  }, [])

  const fetchCertifications = async () => {
    try {
      const response = await fetch('/api/certifications')
      if (!response.ok) throw new Error('Failed to fetch certifications')
      const data = await response.json()
      setCertifications(data)
    } catch (error) {
      console.error('Error fetching certifications:', error)
      toast.error('Failed to fetch certifications')
    } finally {
      setLoading(false)
    }
  }

  const deleteCertification = async (id: string) => {
    if (!confirm('Are you sure you want to delete this certification?')) return

    try {
      const response = await fetch(`/api/certifications/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) throw new Error('Failed to delete certification')
      
      setCertifications(certifications.filter(cert => cert.id !== id))
      toast.success('Certification deleted successfully')
    } catch (error) {
      console.error('Error deleting certification:', error)
      toast.error('Failed to delete certification')
    }
  }

  const togglePublished = async (id: string, published: boolean) => {
    try {
      const response = await fetch(`/api/certifications/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ published: !published }),
      })
      
      if (!response.ok) throw new Error('Failed to update certification')
      
      setCertifications(certifications.map(cert => 
        cert.id === id ? { ...cert, published: !published } : cert
      ))
      toast.success(`Certification ${!published ? 'published' : 'unpublished'}`)
    } catch (error) {
      console.error('Error updating certification:', error)
      toast.error('Failed to update certification')
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-300 rounded w-64"></div>
          <div className="grid gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-32 bg-gray-300 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Certifications</h1>
          <p className="text-muted-foreground">Manage your professional certifications</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Certification
        </Button>
      </div>

      <div className="grid gap-6">
        {certifications.map((cert) => (
          <Card key={cert.id} className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-start">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    {cert.emoji ? (
                      <span className="text-2xl">{cert.emoji}</span>
                    ) : (
                      <Award className="h-6 w-6 text-orange-600" />
                    )}
                  </div>
                  <div>
                    <CardTitle className="text-xl">{cert.title}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                      <div className="flex items-center">
                        <Award className="h-4 w-4 mr-1" />
                        {cert.issuer}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {cert.date}
                      </div>
                      {cert.credentialId && (
                        <div className="text-xs bg-gray-100 px-2 py-1 rounded">
                          ID: {cert.credentialId}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={cert.published ? "default" : "secondary"}>
                    {cert.published ? "Published" : "Draft"}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => togglePublished(cert.id, cert.published)}
                  >
                    {cert.published ? "Unpublish" : "Publish"}
                  </Button>
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteCertification(cert.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{cert.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {certifications.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No certifications found. Add your first certification to get started.</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
