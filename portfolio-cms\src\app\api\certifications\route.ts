import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET() {
  try {
    const certifications = await prisma.certification.findMany({
      orderBy: {
        order: 'asc',
      },
    })

    return withCors(NextResponse.json(certifications))
  } catch (error) {
    console.error('Error fetching certifications:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch certifications' },
      { status: 500 }
    ))
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      issuer,
      date,
      credentialId,
      emoji,
      description,
      order,
      published,
    } = body

    const certification = await prisma.certification.create({
      data: {
        title,
        issuer,
        date,
        credentialId,
        emoji,
        description,
        order: order || 0,
        published: published !== undefined ? published : true,
      },
    })

    return withCors(NextResponse.json(certification, { status: 201 }))
  } catch (error) {
    console.error('Error creating certification:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create certification' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS() {
  return handleOptions()
}
