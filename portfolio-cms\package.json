{"name": "portfolio-cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@google-analytics/data": "^5.1.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@tiptap/extension-code-block-lowlight": "^2.23.1", "@tiptap/extension-color": "^2.23.1", "@tiptap/extension-highlight": "^2.23.1", "@tiptap/extension-image": "^2.23.1", "@tiptap/extension-link": "^2.23.1", "@tiptap/extension-table": "^2.23.1", "@tiptap/extension-table-cell": "^2.23.1", "@tiptap/extension-table-header": "^2.23.1", "@tiptap/extension-table-row": "^2.23.1", "@tiptap/extension-text-style": "^2.23.1", "@tiptap/react": "^2.23.1", "@tiptap/starter-kit": "^2.23.1", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "googleapis": "^150.0.1", "lowlight": "^3.3.0", "lucide-react": "^0.525.0", "next": "15.3.4", "next-auth": "^4.24.11", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-icons": "^5.5.0", "reading-time": "^1.5.0", "slugify": "^1.6.6", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prisma": "^6.10.1", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}